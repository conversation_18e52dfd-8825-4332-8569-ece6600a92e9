[package]
name = "lua-macro-editor"
version = "0.10.0"
description = "Lua Macro Editor for Adeko Libraries"
authors = ["you"]
license = ""
repository = ""
edition = "2021"

# See more keys and their definitions at https://doc.rust-lang.org/cargo/reference/manifest.html

[build-dependencies]
tauri-build = { version = "2.0", features = [] }

[dependencies]
tauri = { version = "2.0", features = [] }
tauri-plugin-shell = "2.0"
tauri-plugin-dialog = "2.0"
serde = { version = "1.0", features = ["derive"] }
serde_json = "1.0"
configparser = "3.0"
dirs = "5.0"
encoding_rs = "0.8"
mlua = { version = "0.9", features = ["lua54", "vendored", "serialize"] }
regex = "1.0"
dxf = "0.6"

[features]
# this feature is used for production builds or when `devPath` points to the filesystem
# DO NOT REMOVE!!
custom-protocol = ["tauri/custom-protocol"]
