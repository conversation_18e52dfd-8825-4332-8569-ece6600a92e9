<template>
  <div class="three-canvas-container">
    <div class="three-toolbar">
      <div class="flex items-center space-x-2">
        <div class="text-xs text-gray-500">
          3D Visualization
        </div>
      </div>
      <div class="flex items-center space-x-1">
        <!-- Removed CSG operation tools -->

        <div class="separator"></div>

        <!-- STL Download Button -->
        <button
          @click="downloadSTL"
          class="p-2 bg-purple-100 hover:bg-purple-200 rounded text-purple-700 hover:text-purple-900 font-medium border border-purple-300"
          title="Download 3D model as STL file"
        >
          <Download :size="16" />
          <span class="ml-1 text-xs">STL</span>
        </button>

        <!-- View Controls -->
        <button
          @click="resetCamera"
          class="p-1 hover:bg-gray-100 rounded text-gray-500 hover:text-gray-700"
          :title="$t('threeCanvas.resetView')"
        >
          <RotateCcw :size="14" />
        </button>
        <button
          @click="fitToView"
          class="p-1 hover:bg-gray-100 rounded text-gray-500 hover:text-gray-700"
          :title="$t('threeCanvas.fitToView')"
        >
          <Maximize2 :size="14" />
        </button>
        <button
          @click="toggleWireframe"
          :class="['p-1 hover:bg-gray-100 rounded', wireframeMode ? 'text-blue-600' : 'text-gray-500 hover:text-gray-700']"
          :title="$t('threeCanvas.wireframe')"
        >
          <Grid3x3 :size="14" />
        </button>
        <button
          @click="toggleOrthographic"
          :class="['p-1 hover:bg-gray-100 rounded', isOrthographic ? 'text-blue-600' : 'text-gray-500 hover:text-gray-700']"
          :title="$t('threeCanvas.orthographic')"
        >
          <Square :size="14" />
        </button>
        <div class="separator"></div>
        <!-- Predefined Views -->
        <div class="view-buttons">
          <button
            @click="setView('front')"
            class="view-btn"
            :title="$t('threeCanvas.frontView')"
          >
            F
          </button>
          <button
            @click="setView('back')"
            class="view-btn"
            :title="$t('threeCanvas.backView')"
          >
            B
          </button>
          <button
            @click="setView('top')"
            class="view-btn"
            :title="$t('threeCanvas.topView')"
          >
            T
          </button>
          <button
            @click="setView('right')"
            class="view-btn"
            :title="$t('threeCanvas.rightView')"
          >
            R
          </button>
          <button
            @click="setView('iso')"
            class="view-btn"
            :title="$t('threeCanvas.isometricView')"
          >
            ISO
          </button>
        </div>
      </div>
    </div>
    
    <div ref="canvasContainer" class="three-canvas-wrapper">
      <!-- Three.js canvas will be inserted here -->

      <!-- Camera Controls Help -->
      <div class="camera-help">
        <div class="help-item">
          <span class="help-label">{{ $t('threeCanvas.rotate') }}:</span>
          <span class="help-text">{{ $t('threeCanvas.leftClick') }}</span>
        </div>
        <div class="help-item">
          <span class="help-label">{{ $t('threeCanvas.zoom') }}:</span>
          <span class="help-text">{{ $t('threeCanvas.mouseWheel') }}</span>
        </div>
        <div class="help-item">
          <span class="help-label">{{ $t('threeCanvas.pan') }}:</span>
          <span class="help-text">{{ $t('threeCanvas.rightClick') }}</span>
        </div>
      </div>
    </div>
    
    <!-- Tool Analysis Panel -->
    <div class="tool-analysis-panel">
      <div class="panel-header">
        <span class="text-xs font-medium text-gray-700">{{ $t('autoTool.detectedTools') }}</span>
      </div>
      <div class="tool-layers-list">
        <div
          v-for="toolLayer in detectedToolLayers"
          :key="toolLayer.layerName"
          class="tool-layer-item"
        >
          <div class="tool-layer-info">
            <div class="layer-name">{{ toolLayer.layerName }}</div>
            <div v-if="toolLayer.analysis.tool" class="tool-info">
              <component :is="getToolIcon(toolLayer.analysis.tool.shape || 'cylindrical')" :size="12" />
              <span class="tool-name">{{ toolLayer.analysis.tool.name }}</span>
              <span class="tool-diameter">⌀{{ toolLayer.analysis.tool.diameter }}mm</span>
            </div>
            <div v-else class="no-tool">{{ $t('autoTool.noToolDetected') }}</div>
            <div class="operation-info">
              <span class="operation">{{ toolLayer.analysis.operation }}</span>
              <span class="surface">{{ $t(`autoTool.${toolLayer.analysis.surface}Surface`) }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, watch, computed } from 'vue'
import { RotateCcw, Grid3x3, Maximize2, Square, Circle, Triangle, Zap, CornerDownRight, Star, Download } from 'lucide-vue-next'
import * as THREE from 'three'
import { STLExporter } from 'three/examples/jsm/exporters/STLExporter.js'
import { OrbitControls } from '../utils/OrbitControls'
import { layerToolDetector } from '@/services/layerToolDetector'
import type { LayerAnalysis } from '@/services/layerToolDetector'
import { cncToolService } from '@/services/cncToolService'


interface DrawCommand {
  command_type: string
  x1: number
  y1: number
  x2: number
  y2: number
  z1?: number // Z coordinate for start point of 3D lines
  z2?: number // Z coordinate for end point of 3D lines
  radius: number
  color: string
  size: number
  text: string
  layer_name: string
  thickness?: number // Thickness/extrusion for 3D representation
  start_angle?: number // Start angle for arcs in degrees
  end_angle?: number   // End angle for arcs in degrees
  clockwise?: boolean  // Arc direction
  svg_path?: string    // SVG path data for complex shapes
  points?: number[][]  // Array of [x, y, z, bulge] points for polylines/polygons
}

interface OperationLayer {
  id: string
  name: string
  operation: 'union' | 'subtract' | 'intersect'
  visible: boolean
  mesh?: THREE.Mesh
}

interface ToolLayer {
  layerName: string
  analysis: LayerAnalysis
  commands: DrawCommand[]
}

interface Props {
  drawCommands: DrawCommand[]
}

const props = withDefaults(defineProps<Props>(), {
  drawCommands: () => []
})

// Three.js scene setup
const canvasContainer = ref<HTMLDivElement>()
let scene: THREE.Scene
let camera: THREE.PerspectiveCamera | THREE.OrthographicCamera
let perspectiveCamera: THREE.PerspectiveCamera
let orthographicCamera: THREE.OrthographicCamera
let renderer: THREE.WebGLRenderer
let controls: OrbitControls
let animationId: number

// Removed CSG evaluator and test functions

// Removed CSG operation determination function

// Enhanced operation detection with more specific patterns
const detectOperationFromLayer = (layerName: string): string => {
  const lowerLayer = layerName.toLowerCase()

  // ADekoLib specific patterns (Turkish CNC software)
  if (lowerLayer.includes('cep_acma') || lowerLayer.includes('cep')) {
    return 'pocketing'
  }
  if (lowerLayer.includes('freze') && lowerLayer.includes('dis')) {
    return 'profiling' // External milling
  }
  if (lowerLayer.includes('freze') && lowerLayer.includes('ic')) {
    return 'pocketing' // Internal milling
  }
  if (lowerLayer.includes('aciliv') || lowerLayer.includes('v')) {
    return 'profiling' // V-groove operations
  }
  if (lowerLayer.includes('freze')) {
    return 'roughing' // General milling
  }
  if (lowerLayer.includes('panel')) {
    return 'roughing' // Panel cutting
  }

  // Standard patterns
  if (lowerLayer.includes('kaba') || lowerLayer.includes('roughing')) {
    return 'roughing'
  }
  if (lowerLayer.includes('son') || lowerLayer.includes('finishing')) {
    return 'finishing'
  }
  if (lowerLayer.includes('pocket')) {
    return 'pocketing'
  }
  if (lowerLayer.includes('delme') || lowerLayer.includes('drilling') || lowerLayer.includes('drill')) {
    return 'drilling'
  }
  if (lowerLayer.includes('profil') || lowerLayer.includes('profile') || lowerLayer.includes('profiling')) {
    return 'profiling'
  }

  // Default based on common patterns
  if (lowerLayer.includes('hole') || lowerLayer.includes('delik')) {
    return 'drilling'
  }
  if (lowerLayer.includes('groove') || lowerLayer.includes('oluk')) {
    return 'profiling'
  }
  if (lowerLayer.includes('frame') || lowerLayer.includes('cerceve')) {
    return 'profiling'
  }

  return 'roughing' // Default operation
}

// Removed CSG toolpath operations function





// Door dimensions from TurtleCanvas.vue (actual model dimensions)
const DOOR_WIDTH = 500
const DOOR_HEIGHT = 700
const DOOR_THICKNESS = 18

// Removed CSG geometry cleanup function

// Coordinate conversion from 2D canvas to 3D scene
const convert2DTo3D = (command: DrawCommand) => {
  // Apply the same coordinate transformations as TurtleCanvas.vue
  // const gridSize = 20  // Used in commented debug code
  const offset = 20
  const materialThickness = 18
  const X = DOOR_WIDTH // Use consistent door width (500)
  const bottomFaceOriginX = 20 + 4 * offset + X + 2 * materialThickness // 616

  // Determine face type and apply appropriate offsets (matching TurtleCanvas.vue exactly)
  const hasSFSuffix = command.layer_name && command.layer_name.includes('_SF')
  const isBottomFaceCommand = command.x1 >= bottomFaceOriginX || hasSFSuffix

  // Grid offset variables (for debugging - currently unused in active code)
  // let gridOffsetX = 0
  // let gridOffsetY = 0

  // if (isBottomFaceCommand) {
  //   // Bottom face operations (matching TurtleCanvas.vue)
  //   gridOffsetX = 620
  //   gridOffsetY = -100
  // } else {
  //   // Top face operations (matching TurtleCanvas.vue)
  //   gridOffsetX = 1 * gridSize
  //   gridOffsetY = -5 * gridSize + 4
  // }

  // Apply the same transformations as TurtleCanvas.vue
  // Note: Canvas coordinates would be calculated here but we use door-relative coordinates below
  // This maintains consistency with the 2D canvas coordinate system

  // Convert from canvas coordinates to 3D world coordinates
  // The door is centered at (0, 0, 0) in 3D space with dimensions DOOR_WIDTH x DOOR_HEIGHT

  // For proper coordinate mapping, we need to understand the relationship between
  // the 2D canvas coordinate system and the 3D door surface

  // The 2D canvas uses a coordinate system where operations are positioned relative to face origins
  // We need to map these to the 3D door surface which is centered at origin

  // Remove the grid offsets to get the actual operation coordinates relative to the door
  let door_x1, door_y1, door_x2, door_y2

  if (isBottomFaceCommand) {
    // Bottom face operations: remove the bottom face offset to get door-relative coordinates
    door_x1 = command.x1 - bottomFaceOriginX
    door_y1 = command.y1
    door_x2 = command.x2 - bottomFaceOriginX
    door_y2 = command.y2
  } else {
    // Top face operations: coordinates are already relative to door
    door_x1 = command.x1
    door_y1 = command.y1
    door_x2 = command.x2
    door_y2 = command.y2
  }

  // Map door-relative coordinates to 3D world coordinates
  // 3D Door orientation: X = width, Y = thickness (depth), Z = height
  // 2D coordinates: X = width, Y = height
  // Therefore: 2D X -> 3D X, 2D Y -> 3D Z
  // Door extends from -DOOR_WIDTH/2 to +DOOR_WIDTH/2 in X
  // Door extends from -DOOR_HEIGHT/2 to +DOOR_HEIGHT/2 in Z
  const x1_3d = door_x1 - DOOR_WIDTH/2
  const z1_3d = door_y1 - DOOR_HEIGHT/2  // 2D Y maps to 3D Z
  const x2_3d = door_x2 - DOOR_WIDTH/2
  const z2_3d = door_y2 - DOOR_HEIGHT/2  // 2D Y maps to 3D Z

  // Debug logging for coordinate conversion (disabled for performance)
  // if (command.layer_name === 'DEFAULT' || command.layer_name === 'TEST_SF') {
  //   console.log(`🔄 Converting 2D to 3D coordinates:`, {
  //     layer: command.layer_name,
  //     original2D: `(${command.x1}, ${command.y1}) -> (${command.x2}, ${command.y2})`,
  //     door2D: `(${door_x1}, ${door_y1}) -> (${door_x2}, ${door_y2})`,
  //     canvas2D: `(${canvas_x1}, ${canvas_y1}) -> (${canvas_x2}, ${canvas_y2})`,
  //     converted3D: `(${x1_3d.toFixed(1)}, ${z1_3d.toFixed(1)}) -> (${x2_3d.toFixed(1)}, ${z2_3d.toFixed(1)})`,
  //     offsets: `(${gridOffsetX}, ${gridOffsetY})`,
  //     bottomFaceOriginX,
  //     isBottomFace: isBottomFaceCommand
  //   })
  // }

  return {
    x1: x1_3d,
    y1: z1_3d,  // Return Z coordinate as Y for compatibility with existing code
    x2: x2_3d,
    y2: z2_3d,  // Return Z coordinate as Y for compatibility with existing code
    isBottomFace: isBottomFaceCommand
  }
}

// Removed CSG operations state
const wireframeMode = ref(false)
const isOrthographic = ref(false)
const operationLayers = ref<OperationLayer[]>([
  {
    id: 'base',
    name: 'Door Base',
    operation: 'union',
    visible: true
  }
])

// Tool analysis state
const detectedToolLayers = computed(() => {
  const layerMap = new Map<string, ToolLayer>()

  props.drawCommands.forEach(command => {
    const layerName = command.layer_name
    if (!layerName) return

    if (!layerMap.has(layerName)) {
      const analysis = layerToolDetector.analyzeLayer(layerName)
      layerMap.set(layerName, {
        layerName,
        analysis,
        commands: []
      })
    }

    layerMap.get(layerName)!.commands.push(command)
  })

  // Filter out non-machinable layers (LUA, LMM, etc.)
  return Array.from(layerMap.values()).filter(toolLayer =>
    toolLayer.analysis.operation !== 'non-machinable'
  )
})

// Initialize Three.js scene
const initThreeJS = () => {
  if (!canvasContainer.value) return

  // Scene
  scene = new THREE.Scene()
  scene.background = new THREE.Color(0xf5f5f5)

  // Cameras
  const aspect = canvasContainer.value.clientWidth / canvasContainer.value.clientHeight

  // Perspective Camera - positioned to show the door centered at origin
  // Improved near/far planes to reduce z-fighting and precision issues
  perspectiveCamera = new THREE.PerspectiveCamera(75, aspect, 1, 5000)
  perspectiveCamera.position.set(400, 300, 400)
  perspectiveCamera.lookAt(0, 0, 0)  // Look at door center

  // Orthographic Camera - positioned to show the door centered at origin
  const frustumSize = 800
  orthographicCamera = new THREE.OrthographicCamera(
    frustumSize * aspect / -2, frustumSize * aspect / 2,
    frustumSize / 2, frustumSize / -2,
    1, 5000  // Improved near/far planes
  )
  orthographicCamera.position.set(400, 300, 400)
  orthographicCamera.lookAt(0, 0, 0)  // Look at door center

  // Set initial camera
  camera = perspectiveCamera

  // Renderer with improved settings for 3D visualization
  renderer = new THREE.WebGLRenderer({
    antialias: true,
    precision: 'highp', // High precision to reduce floating point errors
    logarithmicDepthBuffer: true, // Better depth precision for complex geometry
    alpha: false, // Disable alpha for better performance
    premultipliedAlpha: false,
    preserveDrawingBuffer: false
  })
  renderer.setSize(canvasContainer.value.clientWidth, canvasContainer.value.clientHeight)
  // Temporarily disable shadow mapping to fix proxy error in Three.js 0.160.0
  renderer.shadowMap.enabled = false
  // renderer.shadowMap.type = THREE.PCFSoftShadowMap

  // Improve rendering quality and stability
  renderer.sortObjects = true // Ensure proper object sorting
  renderer.autoClear = true
  renderer.autoClearColor = true
  renderer.autoClearDepth = true
  renderer.autoClearStencil = true

  // Anti-flickering renderer settings
  renderer.setPixelRatio(Math.min(window.devicePixelRatio, 2)) // Limit pixel ratio for stability

  // Depth buffer settings to prevent z-fighting
  const gl = renderer.getContext()
  if (gl) {
    gl.enable(gl.DEPTH_TEST)
    gl.depthFunc(gl.LEQUAL)
    gl.enable(gl.POLYGON_OFFSET_FILL)
  }

  canvasContainer.value.appendChild(renderer.domElement)

  // Controls
  controls = new OrbitControls(camera as THREE.PerspectiveCamera, renderer.domElement)
  controls.enableDampingProperty = true
  controls.dampingFactorProperty = 0.05
  controls.enableZoomProperty = true
  controls.enableRotateProperty = true
  controls.enablePanProperty = true

  // Lighting
  const ambientLight = new THREE.AmbientLight(0x404040, 0.6)
  scene.add(ambientLight)

  const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)
  directionalLight.position.set(100, 100, 50)
  // Disable shadow casting since shadow mapping is disabled
  directionalLight.castShadow = false
  // directionalLight.shadow.mapSize.width = 2048
  // directionalLight.shadow.mapSize.height = 2048
  scene.add(directionalLight)

  // Create door base geometry
  createDoorBase()

  // Start render loop
  animate()
}

// Create the base door geometry
const createDoorBase = () => {
  // Create door geometry with correct orientation
  // In 3D: X = width, Y = thickness (depth), Z = height
  const geometry = new THREE.BoxGeometry(DOOR_WIDTH, DOOR_THICKNESS, DOOR_HEIGHT)

  // Anti-flickering material for base door
  const material = new THREE.MeshPhongMaterial({
    color: 0x8B4513,
    transparent: false, // Remove transparency to prevent flickering
    opacity: 1.0,
    shininess: 20,
    flatShading: false,
    // Anti-flickering settings
    polygonOffset: true,
    polygonOffsetFactor: 0, // Base mesh gets priority
    polygonOffsetUnits: 0,
    depthTest: true,
    depthWrite: true,
    side: THREE.FrontSide
  })

  const doorMesh = new THREE.Mesh(geometry, material)
  // Position door so the top surface is at Y=0 for easier tool positioning
  // Door extends from Y=0 (top surface) to Y=-18 (bottom surface)
  doorMesh.position.set(42.5, -DOOR_THICKNESS/2, 96)
  // Disable shadows since shadow mapping is disabled
  doorMesh.castShadow = false
  doorMesh.receiveShadow = false
  doorMesh.renderOrder = 0 // Render first

  console.log(`🚪 Door positioned at Y=${doorMesh.position.y}, surface at Y=0, extends to Y=${-DOOR_THICKNESS}`)

  scene.add(doorMesh)

  // Store in base layer
  const baseLayer = operationLayers.value.find(l => l.id === 'base')
  if (baseLayer) {
    baseLayer.mesh = doorMesh
  }
}

// Animation loop
const animate = () => {
  animationId = requestAnimationFrame(animate)

  // Update controls
  if (controls) {
    controls.update()
  }

  renderer.render(scene, camera)
}

// Removed CSG operation methods

// Removed CSG operations function

// const removeLayer = (layerId: string) => {
//   if (layerId === 'base') return // Don't remove base layer
//
//   const layerIndex = operationLayers.value.findIndex(l => l.id === layerId)
//   if (layerIndex > -1) {
//     const layer = operationLayers.value[layerIndex]
//     if (layer.mesh) {
//       scene.remove(layer.mesh)
//     }
//     operationLayers.value.splice(layerIndex, 1)
//
//     if (activeLayerId.value === layerId) {
//       activeLayerId.value = 'base'
//     }
//   }
// }

// const setActiveLayer = (layerId: string) => {
//   activeLayerId.value = layerId
// }

// View controls
const resetCamera = () => {
  camera.position.set(400, 300, 400)
  camera.lookAt(200, 200, 0)  // Look at X+Y+ quadrant
  if (controls) {
    controls.targetProperty.set(200, 200, 0)  // Set target to X+Y+ quadrant
    controls.update()
  }
}

const fitToView = () => {
  // Calculate bounding box of all objects
  const box = new THREE.Box3()
  scene.traverse((object) => {
    if (object instanceof THREE.Mesh) {
      box.expandByObject(object)
    }
  })

  if (!box.isEmpty()) {
    const center = box.getCenter(new THREE.Vector3())
    const size = box.getSize(new THREE.Vector3())
    const maxDim = Math.max(size.x, size.y, size.z)
    const fov = camera instanceof THREE.PerspectiveCamera ? camera.fov * (Math.PI / 180) : 0.5
    let cameraZ = Math.abs(maxDim / 2 / Math.tan(fov / 2))

    cameraZ *= 1.5 // Add some padding

    camera.position.set(center.x + cameraZ, center.y + cameraZ, center.z + cameraZ)
    camera.lookAt(center)

    if (controls) {
      controls.targetProperty.copy(center)
      controls.update()
    }
  }
}

const toggleWireframe = () => {
  wireframeMode.value = !wireframeMode.value

  scene.traverse((object) => {
    if (object instanceof THREE.Mesh && object.material instanceof THREE.Material) {
      // Type guard to check if material has wireframe property
      if ('wireframe' in object.material) {
        (object.material as any).wireframe = wireframeMode.value
      }
    }
  })
}

const toggleOrthographic = () => {
  isOrthographic.value = !isOrthographic.value

  // Store current position and target
  const currentPosition = camera.position.clone()
  const currentTarget = controls ? controls.targetProperty.clone() : new THREE.Vector3()

  // Switch camera
  if (isOrthographic.value) {
    camera = orthographicCamera
  } else {
    camera = perspectiveCamera
  }

  // Restore position and target
  camera.position.copy(currentPosition)
  camera.lookAt(currentTarget)

  // Update controls
  if (controls) {
    controls.dispose()
    controls = new OrbitControls(camera as THREE.PerspectiveCamera, renderer.domElement)
    controls.enableDampingProperty = true
    controls.dampingFactorProperty = 0.05
    controls.targetProperty.copy(currentTarget)
    controls.update()
  }
}

// Predefined view positions
const setView = (viewType: 'front' | 'back' | 'top' | 'right' | 'iso') => {
  const distance = 600
  const center = new THREE.Vector3(0, 0, 0)  // Door is now centered at origin

  let position: THREE.Vector3

  switch (viewType) {
    case 'front':
      position = new THREE.Vector3(0, 0, distance)
      break
    case 'back':
      position = new THREE.Vector3(0, 0, -distance)
      break
    case 'top':
      position = new THREE.Vector3(0, distance, 0)
      break
    case 'right':
      position = new THREE.Vector3(distance, 0, 0)
      break
    case 'iso':
    default:
      position = new THREE.Vector3(distance * 0.7, distance * 0.7, distance * 0.7)
      break
  }

  camera.position.copy(position)
  camera.lookAt(center)

  if (controls) {
    controls.targetProperty.copy(center)
    controls.update()
  }
}

// Download STL file of the current 3D model
const downloadSTL = () => {
  try {
    console.log('🔽 Starting STL export...')

    // Find the main door mesh
    const baseLayer = operationLayers.value.find(l => l.id === 'base')
    if (!baseLayer || !baseLayer.mesh) {
      console.warn('⚠️ No door mesh found for STL export')
      alert('No 3D model found to export. Please ensure the door is visible in the 3D view.')
      return
    }

    const meshToExport = baseLayer.mesh
    console.log(`📦 Exporting mesh with ${meshToExport.geometry.attributes.position.count} vertices`)

    // Create STL exporter
    const exporter = new STLExporter()

    // Export the mesh to STL format (binary for smaller file size)
    const stlString = exporter.parse(meshToExport, { binary: false })

    // Create blob and download
    const blob = new Blob([stlString], { type: 'application/octet-stream' })
    const url = URL.createObjectURL(blob)

    // Create download link
    const link = document.createElement('a')
    link.href = url
    link.download = 'door_model.stl'
    document.body.appendChild(link)
    link.click()
    document.body.removeChild(link)

    // Clean up
    URL.revokeObjectURL(url)

    console.log('✅ STL export completed successfully')
  } catch (error) {
    console.error('❌ STL export failed:', error)
    alert('Failed to export STL file. Please check the console for details.')
  }
}

// Handle window resize
const handleResize = () => {
  if (!canvasContainer.value || !camera || !renderer) return

  const width = canvasContainer.value.clientWidth
  const height = canvasContainer.value.clientHeight
  const aspect = width / height

  // Update both cameras
  if (perspectiveCamera) {
    perspectiveCamera.aspect = aspect
    perspectiveCamera.updateProjectionMatrix()
  }

  if (orthographicCamera) {
    const frustumSize = 800
    orthographicCamera.left = frustumSize * aspect / -2
    orthographicCamera.right = frustumSize * aspect / 2
    orthographicCamera.top = frustumSize / 2
    orthographicCamera.bottom = frustumSize / -2
    orthographicCamera.updateProjectionMatrix()
  }

  renderer.setSize(width, height)
}

// Lifecycle
onMounted(() => {
  initThreeJS()
  window.addEventListener('resize', handleResize)
})

onUnmounted(() => {
  if (animationId) {
    cancelAnimationFrame(animationId)
  }
  window.removeEventListener('resize', handleResize)

  if (controls) {
    controls.dispose()
  }

  if (renderer) {
    renderer.dispose()
  }
})

// Watch for draw commands changes and create tool objects for visualization
watch(() => props.drawCommands, (newCommands) => {
  console.log('ThreeCanvas received draw commands:', newCommands.length)
  if (scene) {
    createToolObjects()
  }
}, { immediate: true })

// Create 3D tool objects from draw commands for visualization
const createToolObjects = () => {
  if (!props.drawCommands || props.drawCommands.length === 0) {
    return
  }

  // Clear existing tool objects
  const existingTools = scene.children.filter(child => child.userData?.isToolObject)
  existingTools.forEach(tool => scene.remove(tool))

  // Group commands by layer and create tool objects
  const layerGroups = new Map<string, DrawCommand[]>()

  props.drawCommands.forEach(command => {
    if (!layerGroups.has(command.layer_name)) {
      layerGroups.set(command.layer_name, [])
    }
    layerGroups.get(command.layer_name)!.push(command)
  })

  layerGroups.forEach((commands, layerName) => {
    // Skip non-machinable layers
    if (layerName.toLowerCase().includes('lua') ||
        layerName.toLowerCase().startsWith('lmm')) {
      return
    }

    // Detect tool for this layer
    const tool = cncToolService.detectToolFromLayerName(layerName)
    if (!tool) {
      console.warn(`No tool detected for layer: ${layerName}`)
      return
    }

    // Get thickness from commands (use first command with thickness, or default)
    const commandWithThickness = commands.find(cmd => cmd.thickness !== undefined && cmd.thickness !== null)
    const thickness = commandWithThickness?.thickness ? Math.abs(commandWithThickness.thickness) : 5

    console.log(`Creating tool objects for layer ${layerName} with tool ${tool.name} and thickness ${thickness}`)

    // Detect operation type from layer name
    const operation = detectOperationFromLayer(layerName)

    // Convert 2D coordinates to 3D for all commands
    const convertedCommands = commands.map(cmd => {
      const coords3D = convert2DTo3D(cmd)
      return {
        ...cmd,
        x1_3d: coords3D.x1,
        y1_3d: coords3D.y1,
        x2_3d: coords3D.x2,
        y2_3d: coords3D.y2,
        isBottomFace: coords3D.isBottomFace
      }
    })

    // Create enhanced tool meshes for operations with converted coordinates
    const toolMeshes = cncToolService.createOperationToolMeshes(convertedCommands, tool, operation, thickness)

    toolMeshes.forEach((toolMesh, index) => {
      toolMesh.userData.isToolObject = true
      toolMesh.userData.layerName = layerName
      toolMesh.userData.toolIndex = index
      toolMesh.userData.tool = tool
      toolMesh.userData.operation = operation

      // Set material color based on tool type
      const material = toolMesh.material as THREE.MeshLambertMaterial
      switch (tool.shape) {
        case 'cylindrical':
          material.color.setHex(0x888888)
          break
        case 'conical':
          material.color.setHex(0x666666)
          break
        case 'ballnose':
          material.color.setHex(0x999999)
          break
        case 'radial':
          material.color.setHex(0x777777)
          break
        case 'special':
          material.color.setHex(0x555555)
          break
      }

      // Debug logging for tool positioning
      console.log(`  ➕ Added tool mesh ${index + 1}/${toolMeshes.length} for ${layerName}:`, {
        position: `(${toolMesh.position.x.toFixed(1)}, ${toolMesh.position.y.toFixed(1)}, ${toolMesh.position.z.toFixed(1)})`,
        tool: tool.name,
        operation: operation
      })

      scene.add(toolMesh)
    })
  })
}

// Tool icon helper
const getToolIcon = (shape: string | undefined | null) => {
  if (!shape) return Circle

  const icons = {
    cylindrical: Circle,
    conical: Triangle,
    ballnose: Zap,
    radial: CornerDownRight,
    special: Star
  }
  return icons[shape as keyof typeof icons] || Circle
}



// Expose methods for parent component
defineExpose({
  resetCamera,
  fitToView,
  toggleWireframe,
  toggleOrthographic,
  setView
})
</script>

<style scoped>
.three-canvas-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  background-color: white;
}

.three-toolbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
  height: 40px;
}

.operation-btn {
  padding: 0.25rem;
  border-radius: 0.25rem;
  color: #6b7280;
  transition: colors 0.15s ease-in-out;
}

.operation-btn:hover {
  background-color: #f3f4f6;
  color: #374151;
}

.operation-btn.active {
  background-color: #dbeafe;
  color: #2563eb;
}

.separator {
  width: 1px;
  height: 1rem;
  background-color: #d1d5db;
  margin: 0 0.25rem;
}

.three-canvas-wrapper {
  flex: 1;
  position: relative;
}

.tool-analysis-panel {
  position: absolute;
  top: 10rem;
  right: 0.5rem;
  width: 16rem;
  background-color: white;
  border: 1px solid #e5e7eb;
  border-radius: 0.375rem;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1);
  max-height: 400px;
}

.panel-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.5rem 0.75rem;
  background-color: #f9fafb;
  border-bottom: 1px solid #e5e7eb;
}

.tool-layers-list {
  max-height: 20rem;
  overflow-y: auto;
}

.tool-layer-item {
  padding: 0.5rem 0.75rem;
  border-bottom: 1px solid #f3f4f6;
}

.tool-layer-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.layer-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: #374151;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.tool-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
}

.tool-name {
  color: #4b5563;
}

.tool-diameter {
  color: #6b7280;
  font-family: ui-monospace, SFMono-Regular, "SF Mono", Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
}

.no-tool {
  font-size: 0.75rem;
  color: #9ca3af;
  font-style: italic;
}

.operation-info {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
}

.operation {
  background-color: #dbeafe;
  color: #1e40af;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  text-transform: capitalize;
}

.surface {
  background-color: #dcfce7;
  color: #166534;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
}

.view-buttons {
  display: flex;
  align-items: center;
  gap: 0.25rem;
}

.view-btn {
  padding: 0.25rem 0.5rem;
  font-size: 0.75rem;
  font-weight: 500;
  border-radius: 0.25rem;
  background-color: #f3f4f6;
  color: #4b5563;
  transition: colors 0.15s ease-in-out;
  min-width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.view-btn:hover {
  background-color: #e5e7eb;
  color: #1f2937;
}

.camera-help {
  position: absolute;
  bottom: 0.5rem;
  left: 0.5rem;
  background-color: rgba(0, 0, 0, 0.75);
  color: white;
  font-size: 0.75rem;
  border-radius: 0.25rem;
  padding: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  backdrop-filter: blur(4px);
}

.help-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.help-label {
  font-weight: 500;
  color: #d1d5db;
}

.help-text {
  color: #f3f4f6;
}
</style>
