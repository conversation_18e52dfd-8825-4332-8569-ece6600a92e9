colorfulThemeService.ts:190 ✅ Registered theme: Vibrant Dark (vibrant-dark)
colorfulThemeService.ts:190 ✅ Registered theme: Vibrant Light (vibrant-light)
colorfulThemeService.ts:190 ✅ Registered theme: Neon Dark (neon-dark)
ocjsService.ts:72 OC.js Worker initialized
colorfulThemeService.ts:236 Colorful themes initialized: Array(3)
App.vue:1043 Adeko Lua Editörü başlatıldı
App.vue:1057 Created welcome file: Untitled
EditorGroup.vue:102 Active file in group: group-1751618435226-dm3qef4hj File: Untitled Content length: 48
Editor.vue:66 Initializing Monaco Editor with content: -- <PERSON><PERSON>ı
-- <PERSON><PERSON><PERSON><PERSON> buraya ekleyin

...
Editor.vue:257 Monaco Editor created successfully
Editor.vue:258 Editor value: -- <PERSON><PERSON>
-- <PERSON><PERSON><PERSON><PERSON> bura<PERSON> ekleyin


Editor.vue:259 Container dimensions: Object
colorfulThemeService.ts:224 Applied colorful theme: Vibrant Dark
Editor.vue:279 ✅ Applied colorful theme: vibrant-dark
Editor.vue:291 🔄 Forced re-tokenization
Editor.vue:308 Editor layout forced
Editor.vue:299 Line 1 tokens: Array(1)
Editor.vue:299 Line 2 tokens: Array(1)
Editor.vue:299 Line 3 tokens: Array(1)
Editor.vue:299 Line 4 tokens: Array(1)
useEditorState.ts:120 Creating new file: script.lua with content length: 2787
useEditorState.ts:121 Content preview: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThick...
EditorGroup.vue:102 Active file in group: group-1751618435226-dm3qef4hj File: script.lua Content length: 2787
Editor.vue:66 Initializing Monaco Editor with content: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThick...
Editor.vue:257 Monaco Editor created successfully
Editor.vue:258 Editor value: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  a = 50    -- kenardan marjin
  g = 10    -- cam marjini
  c = 14    -- çita kalınlığı
  d = 6     -- cam derinliği
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()

  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  G.setLayer("H_Freze5mm_Ic")
  G.setThickness(-materialThickness+d-1)
  print(-materialThickness+d-1)
  local h = (Y-2*a)/3
  local t = {}
  t[1]  = {}    t[1][1],  t[1][2],  t[1][3],  t[1][4]  = {a, a}, {X-a, a}, {X/2, a+h/2}, {a, a}
  t[2]  = {}    t[2][1],  t[2][2],  t[2][3],  t[2][4]  = {a, a}, {X/2, a+h/2}, {a, a+h}, {a, a} 
  t[3]  = {}    t[3][1],  t[3][2],  t[3][3],  t[3][4]  = {X/2, a+h/2}, {X-a, a}, {X-a, a+h}, {X/2, a+h/2}
  t[4]  = {}    t[4][1],  t[4][2],  t[4][3],  t[4][4]  = {X/2, a+h/2}, {X-a, a+h}, {X/2, Y/2}, {a, a+h}, {X/2, a+h/2}
  t[5]  = {}    t[5][4],  t[5][3],  t[5][2],  t[5][1]  = {a, Y-a}, {X-a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a}
  t[6]  = {}    t[6][4],  t[6][3],  t[6][2],  t[6][1]  = {a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a-h}, {a, Y-a} 
  t[7]  = {}    t[7][4],  t[7][3],  t[7][2],  t[7][1]  = {X/2, Y-a-h/2}, {X-a, Y-a}, {X-a, Y-a-h}, {X/2, Y-a-h/2}
  t[8]  = {}    t[8][4],  t[8][3],  t[8][2],  t[8][1]  = {X/2, Y-a-h/2}, {X-a, Y-a-h}, {X/2, Y/2}, {a, Y-a-h}, {X/2, Y-a-h/2}
  t[9]  = {}    t[9][1],  t[9][2],  t[9][3],  t[9][4]  = {X/2, Y/2}, {X-a, a+h}, {X-a, a+2*h}, {X/2, Y/2}
  t[10] = {}    t[10][4], t[10][3], t[10][2], t[10][1] = {X/2, Y/2}, {a, a+h}, {a, a+2*h}, {X/2, Y/2}
  G.polylineimp( G.offSet(t[1],  -c/2) )
  G.polylineimp( G.offSet(t[2],  -c/2) )
  G.polylineimp( G.offSet(t[3],  -c/2) )
  G.polylineimp( G.offSet(t[4],  -c/2) )
  G.polylineimp( G.offSet(t[9],  -c/2) )
  G.polylineimp( G.offSet(t[5],  -c/2) )
  G.polylineimp( G.offSet(t[6],  -c/2) )
  G.polylineimp( G.offSet(t[7],  -c/2) )
  G.polylineimp( G.offSet(t[8],  -c/2) )
  G.polylineimp( G.offSet(t[10], -c/2) )
  
  G.setFace("bottom")
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-d)
  G.rectangle({a-g, a-g}, {X-a+g, Y-a+g})
  
  G.setLayer("K_Freze20mm_SF")
  for i = 0, 2, 1
  do
    G.line({a, a+i*h}, {X-a, a+(i+1)*h})
    G.line({X-a, a+i*h}, {a, a+(i+1)*h})
  end
  
  -- model parameters
  G.setFace("top")
  G.showPar({0, a+h/2}, {a+c/2, a+h/2}, "a+c/2")
  G.showPar({X/2, Y-a-c/2}, {X/2, Y}, "a+c/2")
  local o1 = G.offSet(t[1], c/2)
  local o2 = G.offSet(t[2], c/2)
  G.showPar(o1[3], o2[2], "c", 3)
  G.setFace("bottom")
  G.showPar({0, h/2}, {a-g, h/2}, "a-g")
  
  return true
end

require "ADekoDebugMode"
Editor.vue:259 Container dimensions: Object
colorfulThemeService.ts:224 Applied colorful theme: Vibrant Dark
Editor.vue:279 ✅ Applied colorful theme: vibrant-dark
Editor.vue:291 🔄 Forced re-tokenization
Editor.vue:308 Editor layout forced
Editor.vue:299 Line 1 tokens: Array(1)
Editor.vue:299 Line 2 tokens: Array(1)
Editor.vue:299 Line 3 tokens: Array(1)
Editor.vue:299 Line 4 tokens: Array(1)
Editor.vue:299 Line 5 tokens: Array(1)
luaExecutor.ts:70 === SENDING TO RUST ===
luaExecutor.ts:71 Script content: -- ADekoCAM, Model Script
-- System variables (binding box dimensions)...: "X", "Y", "materialThickness"

------------------------------------------------
function modelMain()
  
  a = 50    -- kenardan marjin
  g = 10    -- cam marjini
  c = 14    -- çita kalınlığı
  d = 6     -- cam derinliği
  
  G = ADekoLib
  G.setFace("top")
  G.setThickness(-materialThickness)
  G.makePartShape()

  if (G.parseModelParameters(modelParameters)==false) then
    return false
  end
  
  G.setLayer("H_Freze5mm_Ic")
  G.setThickness(-materialThickness+d-1)
  print(-materialThickness+d-1)
  local h = (Y-2*a)/3
  local t = {}
  t[1]  = {}    t[1][1],  t[1][2],  t[1][3],  t[1][4]  = {a, a}, {X-a, a}, {X/2, a+h/2}, {a, a}
  t[2]  = {}    t[2][1],  t[2][2],  t[2][3],  t[2][4]  = {a, a}, {X/2, a+h/2}, {a, a+h}, {a, a} 
  t[3]  = {}    t[3][1],  t[3][2],  t[3][3],  t[3][4]  = {X/2, a+h/2}, {X-a, a}, {X-a, a+h}, {X/2, a+h/2}
  t[4]  = {}    t[4][1],  t[4][2],  t[4][3],  t[4][4]  = {X/2, a+h/2}, {X-a, a+h}, {X/2, Y/2}, {a, a+h}, {X/2, a+h/2}
  t[5]  = {}    t[5][4],  t[5][3],  t[5][2],  t[5][1]  = {a, Y-a}, {X-a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a}
  t[6]  = {}    t[6][4],  t[6][3],  t[6][2],  t[6][1]  = {a, Y-a}, {X/2, Y-a-h/2}, {a, Y-a-h}, {a, Y-a} 
  t[7]  = {}    t[7][4],  t[7][3],  t[7][2],  t[7][1]  = {X/2, Y-a-h/2}, {X-a, Y-a}, {X-a, Y-a-h}, {X/2, Y-a-h/2}
  t[8]  = {}    t[8][4],  t[8][3],  t[8][2],  t[8][1]  = {X/2, Y-a-h/2}, {X-a, Y-a-h}, {X/2, Y/2}, {a, Y-a-h}, {X/2, Y-a-h/2}
  t[9]  = {}    t[9][1],  t[9][2],  t[9][3],  t[9][4]  = {X/2, Y/2}, {X-a, a+h}, {X-a, a+2*h}, {X/2, Y/2}
  t[10] = {}    t[10][4], t[10][3], t[10][2], t[10][1] = {X/2, Y/2}, {a, a+h}, {a, a+2*h}, {X/2, Y/2}
  G.polylineimp( G.offSet(t[1],  -c/2) )
  G.polylineimp( G.offSet(t[2],  -c/2) )
  G.polylineimp( G.offSet(t[3],  -c/2) )
  G.polylineimp( G.offSet(t[4],  -c/2) )
  G.polylineimp( G.offSet(t[9],  -c/2) )
  G.polylineimp( G.offSet(t[5],  -c/2) )
  G.polylineimp( G.offSet(t[6],  -c/2) )
  G.polylineimp( G.offSet(t[7],  -c/2) )
  G.polylineimp( G.offSet(t[8],  -c/2) )
  G.polylineimp( G.offSet(t[10], -c/2) )
  
  G.setFace("bottom")
  G.setLayer("H_Freze20mm_Ic_SF")
  G.setThickness(-d)
  G.rectangle({a-g, a-g}, {X-a+g, Y-a+g})
  
  G.setLayer("K_Freze20mm_SF")
  for i = 0, 2, 1
  do
    G.line({a, a+i*h}, {X-a, a+(i+1)*h})
    G.line({X-a, a+i*h}, {a, a+(i+1)*h})
  end
  
  -- model parameters
  G.setFace("top")
  G.showPar({0, a+h/2}, {a+c/2, a+h/2}, "a+c/2")
  G.showPar({X/2, Y-a-c/2}, {X/2, Y}, "a+c/2")
  local o1 = G.offSet(t[1], c/2)
  local o2 = G.offSet(t[2], c/2)
  G.showPar(o1[3], o2[2], "c", 3)
  G.setFace("bottom")
  G.showPar({0, h/2}, {a-g, h/2}, "a-g")
  
  return true
end

require "ADekoDebugMode"
luaExecutor.ts:72 Lua library path: ./LIBRARY\luaLibrary
luaExecutor.ts:73 Debug mode: false
luaExecutor.ts:74 === END SENDING TO RUST ===
App.vue:690 Full result object: {
  "success": true,
  "output": "DEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is nil\nDEBUG: ADekoLib.engine is table: 00000291C221BB20\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 00000291C221BB20\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 00000291C221BB20\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 00000291C221BB20\nDEBUG: Engine is available, proceeding with engine calls\n-13\nDEBUG: ADekoLib.engine is table: 00000291C221BB20\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 00000291C221BB20\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 00000291C221BB20\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 00000291C221BB20\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 00000291C221BB20\nDEBUG: Engine is available, proceeding with engine calls\nDEBUG: ADekoLib.engine is table: 00000291C221BB20\nDEBUG: Engine is available, proceeding with engine calls\nDebug model JSON generated:\n{\n  \"models\": {\n    \"debug\": {\n      \"paths\": []\n    }\n  },\n  \"notes\": {\n    \"created\": \"2025-07-04 11:52:57\",\n    \"engine\": \"lua_makerjs_engine\",\n    \"version\": \"1.0.0\"\n  },\n  \"paths\": []\n}\nGeometry model JSON generated:\n{\n  \"models\": {\n    \"H_Freze20mm_Ic_SF\": {\n      \"paths\": {\n        \"rect_bottom_41\": {\n          \"end\": [\n            676.0,\n            756.0\n          ],\n          \"origin\": [\n            1096.0,\n            756.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_left_44\": {\n          \"end\": [\n            676.0,\n            136.0\n          ],\n          \"origin\": [\n            676.0,\n            756.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_right_43\": {\n          \"end\": [\n            1096.0,\n            756.0\n          ],\n          \"origin\": [\n            1096.0,\n            136.0\n          ],\n          \"type\": \"line\"\n        },\n        \"rect_top_42\": {\n          \"end\": [\n            1096.0,\n            136.0\n          ],\n          \"origin\": [\n            676.0,\n            136.0\n          ],\n          \"type\": \"line\"\n        }\n      }\n    },\n    \"H_Freze5mm_Ic\": {\n      \"paths\": {\n        \"polyline_seg_10\": {\n          \"end\": [\n            290.0,\n            238.174\n          ],\n          \"origin\": [\n            460.348,\n            153.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_11\": {\n          \"end\": [\n            119.652,\n            153.0\n          ],\n          \"origin\": [\n            290.0,\n            238.174\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_12\": {\n          \"end\": [\n            274.348,\n            246.0\n          ],\n          \"origin\": [\n            97.0,\n            157.326\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_13\": {\n          \"end\": [\n            97.0,\n            334.674\n          ],\n          \"origin\": [\n            274.348,\n            246.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_14\": {\n          \"end\": [\n            97.0,\n            157.326\n          ],\n          \"origin\": [\n            97.0,\n            334.674\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_15\": {\n          \"end\": [\n            483.0,\n            157.326\n          ],\n          \"origin\": [\n            305.652,\n            246.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_16\": {\n          \"end\": [\n            483.0,\n            334.674\n          ],\n          \"origin\": [\n            483.0,\n            157.326\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_17\": {\n          \"end\": [\n            305.652,\n            246.0\n          ],\n          \"origin\": [\n            483.0,\n            334.674\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_18\": {\n          \"end\": [\n            474.348,\n            346.0\n          ],\n          \"origin\": [\n            290.0,\n            253.826\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_19\": {\n          \"end\": [\n            290.0,\n            438.174\n          ],\n          \"origin\": [\n            474.348,\n            346.0\n          ],\n          \"type\": \"line\"\n        },\n        \"polyline_seg_20\": {\n          \"end\": [\n            105.652,\n            346.0\n          ],\n          \
App.vue:694 Draw commands received: []
App.vue:696 MakerJS JSON received: {
  "models": {
    "H_Freze20mm_Ic_SF": {
      "paths": {
        "rect_bottom_41": {
          "end": [
            676.0,
            756.0
          ],
          "origin": [
            1096.0,
 ...
VisualizationPanel.vue:1082 Processing makerjs JSON: {
  "models": {
    "H_Freze20mm_Ic_SF": {
      "paths": {
        "rect_bottom_41": {
          "end": [
            676.0,
            756.0
          ],
          "origin": [
            1096.0,
 ...
VisualizationPanel.vue:1084 Converted makerjs JSON to 79 draw commands
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1200 x 660
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
GraphicsCanvas.vue:104 drawCanvas called with 59 commands
GraphicsCanvas.vue:138 Canvas size set to: 1200 x 660
GraphicsCanvas.vue:170 Drawing 59 commands
GraphicsCanvas.vue:176 drawCanvas completed
OCJSCanvas.vue:116 🚀 WORKER-BASED PROCESSING STARTED
OCJSCanvas.vue:131 🔍 Parsed commands: Object
OCJSCanvas.vue:140 🔧 Using OCJS Service to extract door parameters
OCJSCanvas.vue:142 📏 Extracted door parameters: Object
OCJSCanvas.vue:152 Door parameters: Object
OCJSCanvas.vue:153 Door size in mm: W=500 H=700 T=18
OCJSCanvas.vue:170 ⚙️ Sending to worker: Object
ocjsService.ts:205 Creating door body with params: Object
ocjsWorker.ts:325 Worker received message: createDoorBody
OCJSCanvas.vue:582 OCJSCanvas mounted
OCJSCanvas.vue:550 🧪 Testing OCJS Worker connectivity...
OCJSCanvas.vue:553 📞 Calling ocjsService.testWorker()
ocjsWorker.ts:53 OpenCascade.js initialized in worker
ocjsWorker.ts:141 Creating box with dimensions: 0.5 0.7 0.018
ocjsWorker.ts:353 Worker completed: createDoorBody
ocjsWorker.ts:325 Worker received message: createDoorBody
ocjsWorker.ts:141 Creating box with dimensions: 100 100 18
ocjsService.ts:209 Door body created successfully: Object
ocjsService.ts:213 Skipping tool processing due to serialization issues
ocjsService.ts:214 Would process 1 top tools and 2 bottom tools
ocjsService.ts:218 Created placeholder GLB, size: 1024 bytes
OCJSCanvas.vue:179 ✅ Worker processing completed, GLB data size: 1024
ocjsWorker.ts:353 Worker completed: createDoorBody
OCJSCanvas.vue:193 Model URL created: blob:http://localhost:1420/b277bb75-db7d-4ab3-95e7-d0b3f7d5e139
ocjsService.ts:190 OC.js Worker test successful
OCJSCanvas.vue:564 ✅ OCJS Worker is working correctly
OCJSCanvas.vue:231 ✅ Initializing Three.js scene
OCJSCanvas.vue:232 📦 Container element: <div data-v-cc8d0f34 class=​"three-viewer-wrapper" style=​"width:​ 100%;​ height:​ 100%;​">​…​</div>​
OCJSCanvas.vue:233 📐 Container dimensions: Object
OCJSCanvas.vue:252 Container dimensions: Object
OCJSCanvas.vue:267 Renderer created with size: 1200 x 600
OCJSCanvas.vue:268 Canvas element: <canvas data-engine=​"three.js r160" width=​"1200" height=​"600" style=​"display:​ block;​ width:​ 1200px;​ height:​ 600px;​ touch-action:​ none;​">​
OCJSCanvas.vue:269 Canvas style: display: block; width: 1200px; height: 600px;
OCJSCanvas.vue:303 Test cube added to scene at position: _Vector3
OCJSCanvas.vue:308 ✅ Three.js scene initialized successfully
OCJSCanvas.vue:320 🔄 Loading GLB model: blob:http://localhost:1420/b277bb75-db7d-4ab3-95e7-d0b3f7d5e139
OCJSCanvas.vue:384 Loading progress: 100%
OCJSCanvas.vue:387  Error loading GLB model: SyntaxError: Unexpected token '